//
//  SubscriptionBannerSection.swift
//  ztt2
//
//  Created by AI Assistant on 2025/8/2.
//

import SwiftUI

/**
 * 订阅导航标签组件
 * 叠加在个人信息组件上方，显示升级会员的提示和按钮
 * 支持试用状态显示和领取入口
 */
struct SubscriptionBannerSection: View {

    // MARK: - Properties
    let onViewPlansPressed: () -> Void

    // MARK: - State
    @State private var sectionAppeared = false
    @State private var isPressed = false
    @State private var showTrialModal = false

    // MARK: - Dependencies
    @StateObject private var trialManager = TrialManager.shared
    
    var body: some View {
        ZStack {
            // 美化背景容器 - 增强渐变和阴影效果
            RoundedRectangle(cornerRadius: DesignSystem.ProfilePage.SubscriptionBanner.cornerRadius)
                .fill(
                    LinearGradient(
                        gradient: Gradient(stops: [
                            .init(color: Color(hex: "#c8e6a0"), location: 0.0),
                            .init(color: DesignSystem.Colors.profileSubscriptionBannerBackground, location: 0.5),
                            .init(color: Color(hex: "#a8d070"), location: 1.0)
                        ]),
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .frame(height: DesignSystem.ProfilePage.SubscriptionBanner.height + 10)
                .shadow(color: Color(hex: "#a9d051").opacity(0.25), radius: 8, x: 0, y: 4)
                .shadow(color: Color.black.opacity(0.06), radius: 3, x: 0, y: 1)
                .overlay(
                    RoundedRectangle(cornerRadius: DesignSystem.ProfilePage.SubscriptionBanner.cornerRadius)
                        .stroke(
                            LinearGradient(
                                gradient: Gradient(colors: [
                                    Color.white.opacity(0.6),
                                    Color.white.opacity(0.2)
                                ]),
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            ),
                            lineWidth: 1.5
                        )
                )

            // 增强装饰性背景元素 - 添加更多层次
            Circle()
                .fill(
                    RadialGradient(
                        gradient: Gradient(colors: [
                            Color.white.opacity(0.15),
                            Color.white.opacity(0.05)
                        ]),
                        center: .center,
                        startRadius: 5,
                        endRadius: 20
                    )
                )
                .frame(width: 40, height: 40)
                .offset(x: -80, y: -15)

            Circle()
                .fill(
                    RadialGradient(
                        gradient: Gradient(colors: [
                            Color.white.opacity(0.12),
                            Color.white.opacity(0.03)
                        ]),
                        center: .center,
                        startRadius: 3,
                        endRadius: 15
                    )
                )
                .frame(width: 30, height: 30)
                .offset(x: 100, y: 20)

            // 添加微妙的光泽效果
            RoundedRectangle(cornerRadius: DesignSystem.ProfilePage.SubscriptionBanner.cornerRadius)
                .fill(
                    LinearGradient(
                        gradient: Gradient(colors: [
                            Color.white.opacity(0.15),
                            Color.clear,
                            Color.clear
                        ]),
                        startPoint: .top,
                        endPoint: .bottom
                    )
                )
                .frame(height: DesignSystem.ProfilePage.SubscriptionBanner.height + 10)
            
            // 升级文案 - 左侧绝对定位，增强文字效果
            HStack {
                VStack(alignment: .leading, spacing: 2) {
                    Spacer()
                    Text(bannerText)
                        .font(.system(
                            size: DesignSystem.ProfilePage.SubscriptionBanner.textFont,
                            weight: .semibold
                        ))
                        .foregroundColor(.white)
                        .lineLimit(1)
                        .shadow(color: Color.black.opacity(0.2), radius: 2, x: 0, y: 1)
                        .shadow(color: Color(hex: "#4da87a").opacity(0.3), radius: 1, x: 0, y: 0.5)
                    Spacer()
                }
                .padding(.leading, DesignSystem.ProfilePage.SubscriptionBanner.padding)
                .opacity(sectionAppeared ? 1.0 : 0.0)
                .offset(x: sectionAppeared ? 0 : -30)
                .animation(.easeOut(duration: 0.8).delay(0.2), value: sectionAppeared)

                Spacer()
            }
            
            // 查看方案按钮 - 右侧绝对定位
            HStack {
                Spacer()
                VStack {
                    Spacer()
                    Button(action: {
                        withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                            isPressed = true
                            handleButtonPressed()
                        }

                        // 恢复按钮状态
                        DispatchQueue.main.asyncAfter(deadline: .now() + 0.15) {
                            isPressed = false
                        }
                    }) {
                        Text(buttonText)
                            .font(.system(
                                size: DesignSystem.ProfilePage.SubscriptionBanner.buttonFont,
                                weight: .bold
                            ))
                            .foregroundColor(.white)
                            .padding(.horizontal, 16)
                            .frame(height: DesignSystem.ProfilePage.SubscriptionBanner.buttonHeight)
                            .background(
                                ZStack {
                                    // 主要渐变背景
                                    LinearGradient(
                                        gradient: Gradient(stops: [
                                            .init(color: Color(hex: "#6bc99a"), location: 0.0),
                                            .init(color: DesignSystem.Colors.profileSubscriptionButtonBackground, location: 0.5),
                                            .init(color: Color(hex: "#4da87a"), location: 1.0)
                                        ]),
                                        startPoint: .topLeading,
                                        endPoint: .bottomTrailing
                                    )

                                    // 顶部高光效果
                                    LinearGradient(
                                        gradient: Gradient(colors: [
                                            Color.white.opacity(0.3),
                                            Color.clear
                                        ]),
                                        startPoint: .top,
                                        endPoint: .center
                                    )
                                }
                            )
                            .cornerRadius(DesignSystem.ProfilePage.SubscriptionBanner.buttonCornerRadius)
                            .shadow(color: Color(hex: "#5dbb93").opacity(0.4), radius: 6, x: 0, y: 3)
                            .shadow(color: Color.black.opacity(0.1), radius: 2, x: 0, y: 1)
                            .overlay(
                                RoundedRectangle(cornerRadius: DesignSystem.ProfilePage.SubscriptionBanner.buttonCornerRadius)
                                    .stroke(
                                        LinearGradient(
                                            gradient: Gradient(colors: [
                                                Color.white.opacity(0.4),
                                                Color.white.opacity(0.1)
                                            ]),
                                            startPoint: .topLeading,
                                            endPoint: .bottomTrailing
                                        ),
                                        lineWidth: 1.2
                                    )
                            )
                    }
                    .buttonStyle(PlainButtonStyle())
                    .scaleEffect(isPressed ? 0.95 : 1.0)
                    .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isPressed)
                    Spacer()
                }
                .padding(.trailing, DesignSystem.ProfilePage.SubscriptionBanner.padding)
                .opacity(sectionAppeared ? 1.0 : 0.0)
                .offset(x: sectionAppeared ? 0 : 20)
                .animation(.spring(response: 0.6, dampingFraction: 0.7).delay(0.4), value: sectionAppeared)
            }
        }
        .frame(height: DesignSystem.ProfilePage.SubscriptionBanner.height + 10)
        .scaleEffect(sectionAppeared ? 1.0 : 0.9)
        .opacity(sectionAppeared ? 1.0 : 0.0)
        .animation(.spring(response: 0.7, dampingFraction: 0.8).delay(0.1), value: sectionAppeared)
        .onAppear {
            withAnimation {
                sectionAppeared = true
            }
        }
        .sheet(isPresented: $showTrialModal) {
            TrialClaimModal(isPresented: $showTrialModal) {
                Task {
                    let success = await trialManager.claimTrial()
                    if success {
                        // 试用领取成功，可以添加成功提示
                        print("✅ 试用领取成功")
                    }
                }
            }
        }
    }

    // MARK: - Computed Properties

    /**
     * 横幅文案
     */
    private var bannerText: String {
        let trialInfo = trialManager.getTrialDisplayInfo()
        switch trialInfo.status {
        case .available:
            return "trial.banner.available_text".localized
        case .active, .expired:
            return "profile.subscription.banner_text".localized
        }
    }

    /**
     * 按钮文案
     */
    private var buttonText: String {
        let trialInfo = trialManager.getTrialDisplayInfo()
        return trialInfo.buttonText
    }

    // MARK: - Methods

    /**
     * 处理按钮点击
     */
    private func handleButtonPressed() {
        let trialInfo = trialManager.getTrialDisplayInfo()

        switch trialInfo.status {
        case .available:
            // 显示试用领取弹窗
            showTrialModal = true
        case .active, .expired:
            // 跳转到订阅页面
            onViewPlansPressed()
        }
    }
}

// MARK: - Preview
#Preview {
    VStack(spacing: 20) {
        // 模拟个人信息卡片背景
        Rectangle()
            .fill(DesignSystem.Colors.profileUserInfoBackground)
            .frame(height: 120)
            .cornerRadius(16)
            .overlay(
                Text("个人信息卡片")
                    .foregroundColor(.gray)
            )
        
        // 订阅标签组件
        SubscriptionBannerSection {
            print("查看会员方案按钮被点击")
        }
        .offset(y: -40) // 模拟叠加效果
        
        Spacer()
    }
    .padding()
    .background(DesignSystem.Colors.background)
} 
