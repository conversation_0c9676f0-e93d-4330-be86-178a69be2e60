//
//  RevenueCatManager.swift
//  ztt2
//
//  Created by AI Assistant on 2025/8/4.
//

import Foundation
import RevenueCat
import StoreKit
import Combine

/**
 * RevenueCat订阅管理器
 * 负责处理订阅购买、恢复、状态检查和与CoreData的同步
 * 支持 free / basic / premium 三个等级
 */
@MainActor
class RevenueCatManager: NSObject, ObservableObject {
    
    // MARK: - Shared Instance
    static let shared = RevenueCatManager()

    // MARK: - Published Properties
    @Published var customerInfo: CustomerInfo?
    @Published var offerings: Offerings?
    @Published var currentSubscriptionLevel: SubscriptionLevel = .free
    @Published var isInitialized: Bool = false
    @Published var isLoading: Bool = false
    @Published var errorMessage: String?
    @Published var purchaseSuccess: Bool = false
    @Published var expirationDate: Date?

    // MARK: - Private Properties
    private var cancellables = Set<AnyCancellable>()
    private var trialManager: TrialManager {
        return TrialManager.shared
    }
    private let dataManager = DataManager.shared

    // MARK: - Product IDs
    enum ProductIDs {
        static let basicMonthly = "com.ztt2.subscription.monthly.basic"
        static let basicYearly = "com.ztt2.subscription.yearly.basic"
        static let premiumMonthly = "com.ztt2.subscription.monthly.premium"
        static let premiumYearly = "com.ztt2.subscription.yearly.premium"
    }

    // MARK: - Entitlement IDs
    enum EntitlementIDs {
        static let basicMember = "basic_member"
        static let premiumMember = "premium_member"
    }
    
    // MARK: - Subscription Levels
    enum SubscriptionLevel: String, CaseIterable {
        case free = "free"
        case basic = "basic"
        case premium = "premium"
        
        var displayName: String {
            switch self {
            case .free:
                return "免费用户"
            case .basic:
                return "初级会员"
            case .premium:
                return "高级会员"
            }
        }
        
        var maxMembers: Int {
            // 移除成员数量限制，所有用户都可以创建无限成员
            return Int.max
        }
        
        var hasCloudSync: Bool {
            switch self {
            case .free:
                return false
            case .basic, .premium:
                return true
            }
        }
        
        var hasAIAnalysis: Bool {
            switch self {
            case .free, .basic:
                return false
            case .premium:
                return true
            }
        }
        
        var hasAdvancedLottery: Bool {
            switch self {
            case .free:
                return false
            case .basic:
                return true // 只有大转盘
            case .premium:
                return true // 所有抽奖功能
            }
        }

        var hasBlindBoxAndScratch: Bool {
            switch self {
            case .free, .basic:
                return false
            case .premium:
                return true // 只有高级会员才有盲盒和刮刮卡
            }
        }
    }
    
    // MARK: - Initialization
    private override init() {
        super.init()
        setupObservers()
    }
    
    // MARK: - Configuration
    
    /**
     * 配置RevenueCat SDK
     * 在App启动时调用，需要提供API Key
     */
    func configure(apiKey: String, userId: String? = nil) {
        guard !apiKey.isEmpty else {
            print("❌ RevenueCat API Key不能为空")
            errorMessage = "subscription.error.configuration_failed".localized
            return
        }
        
        // 配置RevenueCat
        Purchases.logLevel = .debug
        Purchases.configure(withAPIKey: apiKey, appUserID: userId)
        
        // 设置delegate
        Purchases.shared.delegate = self
        
        // 获取初始数据
        Task {
            await loadInitialData()
        }
        
        print("✅ RevenueCat已配置，API Key: \(apiKey)")
    }
    
    /**
     * 加载初始数据
     */
    private func loadInitialData() async {
        isLoading = true
        
        do {
            // 获取用户信息
            let customerInfo = try await Purchases.shared.customerInfo()
            await updateCustomerInfo(customerInfo)
            
            // 获取产品信息
            let offerings = try await Purchases.shared.offerings()
            await MainActor.run {
                self.offerings = offerings
                self.isInitialized = true
                self.isLoading = false
            }
            
            print("✅ RevenueCat初始数据加载完成")
            
        } catch {
            await MainActor.run {
                self.errorMessage = "subscription.error.loading_failed".localized
                self.isLoading = false
            }
            print("❌ RevenueCat初始数据加载失败: \(error)")
        }
    }
    
    // MARK: - Purchase Methods
    
    /**
     * 购买订阅产品
     */
    func purchaseProduct(productId: String) async -> Bool {
        guard let offerings = offerings else {
            await MainActor.run {
                self.errorMessage = "subscription.error.no_products".localized
            }
            return false
        }
        
        // 查找对应的Package
        guard let package = findPackage(productId: productId, in: offerings) else {
            await MainActor.run {
                self.errorMessage = "subscription.error.product_not_found".localized
            }
            return false
        }
        
        await MainActor.run {
            self.isLoading = true
            self.errorMessage = nil
            self.purchaseSuccess = false
        }
        
        print("🆔 准备购买产品: \(productId)")
        
        do {
            let result = try await Purchases.shared.purchase(package: package)
            
            // 检查购买结果
            if !result.userCancelled {
                print("✅ 购买成功")
                await updateCustomerInfo(result.customerInfo)
                
                // 触发成功购买的处理
                await handleSuccessfulPurchase(productId: productId)
                
                await MainActor.run {
                    self.isLoading = false
                    self.errorMessage = nil
                    self.purchaseSuccess = true
                }
                return true
            } else {
                // 用户取消了购买
                print("ℹ️ 用户取消了购买")
                await MainActor.run {
                    self.isLoading = false
                    self.errorMessage = "用户取消了购买"
                }
                return false
            }
            
        } catch {
            await MainActor.run {
                self.isLoading = false
                self.errorMessage = extractDetailedError(from: error)
            }
            print("❌ 购买失败: \(error)")
            return false
        }
    }
    
    /**
     * 检查订阅状态（替代恢复购买）
     * 对于订阅类型产品，直接检查当前用户的订阅状态
     */
    func restorePurchases() async -> Bool {
        await MainActor.run {
            self.isLoading = true
            self.errorMessage = nil
        }
        
        do {
            // 对于订阅类型，直接获取最新的用户信息来检查订阅状态
            let customerInfo = try await Purchases.shared.customerInfo()
            await updateCustomerInfo(customerInfo)
            
            await MainActor.run {
                self.isLoading = false
            }
            
            // 检查是否有活跃订阅
            let hasActiveSubscription = !customerInfo.activeSubscriptions.isEmpty
            if hasActiveSubscription {
                print("✅ 检测到活跃订阅")
                await MainActor.run {
                    self.purchaseSuccess = true
                }
            } else {
                print("ℹ️ 未检测到活跃订阅")
            }

            return hasActiveSubscription
            
        } catch {
            await MainActor.run {
                self.isLoading = false
                self.errorMessage = extractDetailedError(from: error)
            }
            print("❌ 订阅状态检查失败: \(error)")
            return false
        }
    }
    
    // MARK: - Private Helper Methods
    
    /**
     * 查找对应的Package
     */
    private func findPackage(productId: String, in offerings: Offerings) -> Package? {
        // 在当前offering中查找
        if let currentOffering = offerings.current {
            for package in currentOffering.availablePackages {
                if package.storeProduct.productIdentifier == productId {
                    return package
                }
            }
        }
        
        // 在所有offerings中查找
        for offering in offerings.all.values {
            for package in offering.availablePackages {
                if package.storeProduct.productIdentifier == productId {
                    return package
                }
            }
        }
        
        return nil
    }
    
    /**
     * 提取详细错误信息
     */
    private func extractDetailedError(from error: Error) -> String {
        let errorDescription = error.localizedDescription.lowercased()

        // 记录详细错误信息用于调试
        print("🔍 详细错误信息: \(error)")

        if errorDescription.contains("network") || errorDescription.contains("internet") {
            return "网络连接失败，请检查网络设置"
        } else if errorDescription.contains("payment") || errorDescription.contains("billing") {
            return "支付失败，请检查支付方式"
        } else if errorDescription.contains("cancelled") || errorDescription.contains("cancel") {
            return "用户取消了购买"
        } else if errorDescription.contains("invalid") {
            return "产品信息无效，请稍后重试"
        } else if errorDescription.contains("attconsentstatus") || errorDescription.contains("attribute") {
            // ATT相关错误通常不影响购买功能，只是记录日志
            print("⚠️ ATT属性同步警告（不影响购买功能）: \(error)")
            return "购买可能成功，正在验证..."
        } else {
            return "购买失败，请稍后重试"
        }
    }
    
    /**
     * 处理成功购买
     */
    private func handleSuccessfulPurchase(productId: String) async {
        // 发送购买成功通知
        NotificationCenter.default.post(name: .subscriptionPurchaseSuccess, object: productId)
        
        // 同步到CoreData
        await syncSubscriptionToDataManager()
        
        print("✅ 购买成功处理完成: \(productId)")
    }
    
    /**
     * 更新客户信息
     */
    private func updateCustomerInfo(_ customerInfo: CustomerInfo) async {
        await MainActor.run {
            self.customerInfo = customerInfo
            self.currentSubscriptionLevel = determineSubscriptionLevel(from: customerInfo)
            self.expirationDate = getExpirationDate(from: customerInfo)
        }
        
        // 同步到DataManager
        await syncSubscriptionToDataManager()
        
        print("📱 订阅状态更新: \(currentSubscriptionLevel.displayName)")
    }
    
    /**
     * 确定订阅等级
     */
    private func determineSubscriptionLevel(from customerInfo: CustomerInfo) -> SubscriptionLevel {
        // 1. 优先检查权限（Entitlements）
        if let premiumEntitlement = customerInfo.entitlements[EntitlementIDs.premiumMember],
           premiumEntitlement.isActive {
            return .premium
        }

        if let basicEntitlement = customerInfo.entitlements[EntitlementIDs.basicMember],
           basicEntitlement.isActive {
            return .basic
        }

        // 2. 检查活跃的订阅产品ID
        for productId in customerInfo.activeSubscriptions {
            if productId.contains("premium") {
                return .premium
            } else if productId.contains("basic") {
                return .basic
            }
        }

        // 3. 检查试用状态（仅在没有正式订阅时）
        if trialManager.isTrialActive {
            return .premium
        }

        return .free
    }
    
    /**
     * 获取到期日期
     * 优先返回RevenueCat订阅到期时间，然后返回试用到期时间
     */
    private func getExpirationDate(from customerInfo: CustomerInfo) -> Date? {
        // 1. 优先检查权限的到期时间
        if let premiumEntitlement = customerInfo.entitlements[EntitlementIDs.premiumMember],
           premiumEntitlement.isActive {
            return premiumEntitlement.expirationDate
        }

        if let basicEntitlement = customerInfo.entitlements[EntitlementIDs.basicMember],
           basicEntitlement.isActive {
            return basicEntitlement.expirationDate
        }

        // 2. 检查所有订阅的到期时间，返回最新的
        var latestDate: Date?

        // 遍历所有权限的到期时间
        for entitlement in customerInfo.entitlements.all.values {
            if let expirationDate = entitlement.expirationDate {
                if latestDate == nil || expirationDate > latestDate! {
                    latestDate = expirationDate
                }
            }
        }

        // 3. 如果没有正式订阅，检查试用状态
        if latestDate == nil && trialManager.isTrialActive {
            return trialManager.trialExpirationDate
        }

        return latestDate
    }
    
    /**
     * 同步订阅状态到DataManager
     */
    private func syncSubscriptionToDataManager() async {
        await MainActor.run {
            // 使用同步管理器进行同步
            SubscriptionSyncManager.shared.syncSubscriptionStatus()
        }
    }
    
    /**
     * 刷新订阅状态（用于试用状态变化时）
     */
    private func refreshSubscriptionStatus() {
        if let customerInfo = customerInfo {
            currentSubscriptionLevel = determineSubscriptionLevel(from: customerInfo)
            expirationDate = getExpirationDate(from: customerInfo)
        } else {
            // 没有RevenueCat信息时，仅基于试用状态判断
            currentSubscriptionLevel = trialManager.isTrialActive ? .premium : .free
            expirationDate = trialManager.isTrialActive ? trialManager.trialExpirationDate : nil
        }

        // 发送状态变更通知
        NotificationCenter.default.post(name: .subscriptionStatusChanged, object: nil)
    }

    /**
     * 设置观察者
     */
    private func setupObservers() {
        // 监听订阅状态变化
        NotificationCenter.default.publisher(for: .subscriptionStatusChanged)
            .sink { [weak self] _ in
                Task { @MainActor in
                    await self?.syncSubscriptionToDataManager()
                }
            }
            .store(in: &cancellables)

        // 监听试用状态变化
        trialManager.$isTrialActive
            .sink { [weak self] _ in
                Task { @MainActor in
                    self?.refreshSubscriptionStatus()
                }
            }
            .store(in: &cancellables)
    }
}

// MARK: - PurchasesDelegate

extension RevenueCatManager: PurchasesDelegate {

    /**
     * 当订阅状态发生变化时调用
     */
    nonisolated func purchases(_ purchases: Purchases, receivedUpdated customerInfo: CustomerInfo) {
        Task { @MainActor in
            await updateCustomerInfo(customerInfo)
            print("📢 RevenueCat订阅状态更新")

            // 检查是否有活跃的订阅，如果有则标记购买成功
            let hasActiveSubscription = !customerInfo.activeSubscriptions.isEmpty
            if hasActiveSubscription && isLoading {
                print("✅ 检测到活跃订阅，标记购买成功")
                purchaseSuccess = true
                isLoading = false
                errorMessage = nil
            }
        }
    }

    /**
     * 处理推广产品购买
     */
    nonisolated func purchases(_ purchases: Purchases, readyForPromotedProduct product: StoreProduct, purchase startPurchase: @escaping StartPurchaseBlock) {
        print("📱 准备推广产品购买: \(product.localizedTitle)")
        startPurchase { (transaction, customerInfo, error, cancelled) in
            if let error = error {
                print("❌ 推广产品购买错误: \(error.localizedDescription)")
            } else if cancelled {
                print("🚫 推广产品购买已取消")
            } else {
                print("✅ 推广产品购买成功")
            }
        }
    }
}

// MARK: - Notification Names

extension Notification.Name {
    static let revenueCatSubscriptionStatusChanged = Notification.Name("revenueCatSubscriptionStatusChanged")
    static let subscriptionPurchaseSuccess = Notification.Name("subscriptionPurchaseSuccess")
}
